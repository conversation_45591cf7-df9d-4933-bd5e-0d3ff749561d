import { IMxUser, LoginOption, MxLoginType } from './mxUser';
import { User } from '@moxo/proto';
import { RequestPromise } from '../network/ajax';
import { ReadonlyDeep } from 'type-fest';
import { deepFreeze } from '@moxo/shared';
import { verifyToken } from './auth';
const UserSymbol = Symbol('user');
export class MxUserImpl implements IMxUser {
  [UserSymbol]: User;
  get isLogin() {
    return !!this[UserSymbol]?.id;
  }
  get id() {
    return this[UserSymbol].id;
  }

  constructor(user: User) {
    this[UserSymbol] = user || { id: '' };
  }

  login(opt?: LoginOption): RequestPromise<IMxUser> {
    let isAutoLogin: boolean = !opt || !opt.pass;
    if (opt) {
      if (
        opt.loginType === MxLoginType.VERIFICATION_CODE ||
        opt.loginType === MxLoginType.ORG_INVITE_TOKEN ||
        opt.loginType === MxLoginType.APPLE_JWT ||
        opt.loginType === MxLoginType.GOOGLE_JWT ||
        opt.loginType === MxLoginType.SALESFORCE_JWT
      ) {
        isAutoLogin = false;
      }
    }

    if (isAutoLogin && this.isLogin) {
      // already login?
      return RequestPromise.createResolve(this);
    }
    if (isAutoLogin) {
      verifyToken();
    } else {
      loginMep(opt);
    }
  }
}
