import {
  ClientParam,
  ClientRequestParameter,
  ClientRequestType,
  ClientResponse,
  ClientResponseCode,
  GroupAccessType,
  User,
  UserRole,
  UserType,
} from '@moxo/proto';
import sdkConfig from '../common/config';
import { sendBizServerRequest } from '../common/bizServerRequest';
import { IRequestPromise, RequestPromise } from '../network/ajax';
import { requestNode, userRequestNode } from '../common/requestNode';
import { LoginOption, MxLoginType, MxRegisterUserMethod } from './mxUser';
import { BizServerError } from '../common/bizServerError';
import { MxRegisterUserOption, MxTokenType, MxVerifyCodeAction } from './userDefines';

export function verifyToken(
  accessToken: string = '',
  rememberMe: boolean = true,
): IRequestPromise<ClientResponse> {
  let params: ClientParam[] = [];

  if (accessToken && sdkConfig.useCookies) {
    params.push({
      name: ClientRequestParameter.USER_REQUEST_READ_SET_COOKIE,
    });

    if (rememberMe) {
      params.push({
        name: ClientRequestParameter.USER_REQUEST_LOGIN_REMEMBER,
      });
    }
  }

  return sendBizServerRequest(requestNode(ClientRequestType.USER_REQUEST_VERIFY_TOKEN, params), {
    accessToken: accessToken,
  });
}
export function getAccessTokenByEmailPassword(opt: LoginOption): IRequestPromise<ClientResponse> {
  let params: ClientParam[] = [
    {
      name: ClientRequestParameter.USER_REQUEST_GET_ACCESS_TOKEN,
    },
  ];

  if (opt.isSuperAdmin || opt.isPartnerAdmin) {
    params.push({
      name: ClientRequestParameter.USER_REQUEST_LOGIN_PARTNER_ADMIN_EXPECTED,
    });
  }

  let user: User = {
    email: opt.email,
    phone_number: opt.phone_number,
    pass: opt.pass,
    first_name: opt.firstName,
    last_name: opt.lastName,
    title: opt.title,
  };

  if (opt.deviceId) {
    user.user_devices = [{ device_id: opt.deviceId }];
  }

  if (opt.rememberDevice) {
    params.push({
      name: ClientRequestParameter.USER_REQUEST_REMEMBER_DEVICE,
    });
  }

  if (opt.verificationCode) {
    params.push({
      name:
        opt.verificationCodeType === 'email'
          ? ClientRequestParameter.USER_REQUEST_EMAIL_CODE
          : ClientRequestParameter.USER_REQUEST_SMS_CODE,
      string_value: opt.verificationCode,
    });
  }

  if (opt.appleJWT) {
    params.push({
      name: ClientRequestParameter.USER_REQUEST_APPLE_JWT,
      string_value: opt.appleJWT,
    });
  }

  if (opt.googleJWT) {
    params.push({
      name: ClientRequestParameter.USER_REQUEST_GOOGLE_JWT,
      string_value: opt.googleJWT,
    });
  }

  if (opt.salesforceJWT) {
    params.push({
      //is different for some reason
      //name: ClientRequestParameter.USER_REQUEST_SALESFORCE_JWT,
      name: ClientRequestParameter.CLIENT_PARAM_JWT,
      string_value: opt.salesforceJWT,
    });
  }

  if (opt.orgInviteToken) {
    params.push({
      name: ClientRequestParameter.GROUP_REQUEST_INVITE_TOKEN,
      string_value: opt.orgInviteToken,
    });
  }

  let reqType: ClientRequestType = ClientRequestType.USER_REQUEST_LOGIN;
  if (opt.loginType === MxLoginType.VERIFICATION_CODE) {
    reqType = ClientRequestType.USER_REQUEST_LOGIN_LOCAL_USER_BY_VERIFICATION_CODE;
  } else if (opt.loginType === MxLoginType.APPLE_JWT) {
    reqType = ClientRequestType.USER_REQUEST_LOGIN_LOCAL_USER_BY_APPLE_ID;
  } else if (opt.loginType === MxLoginType.GOOGLE_JWT) {
    reqType = ClientRequestType.USER_REQUEST_LOGIN_LOCAL_USER_BY_GOOGLE_ID;
  } else if (opt.loginType === MxLoginType.SALESFORCE_JWT) {
    reqType = ClientRequestType.USER_REQUEST_LOGIN_LOCAL_USER_BY_SALESFORCE_ID;
  } else if (opt.loginType === MxLoginType.ORG_INVITE_TOKEN) {
    reqType = ClientRequestType.USER_REQUEST_LOGIN_LOCAL_USER_BY_GROUP_INVITATION_TOKEN;
  }

  return sendBizServerRequest(userRequestNode(reqType, user, params));
}
export function getAccessToken(): RequestPromise<ClientResponse> {
  return sendBizServerRequest(requestNode(ClientRequestType.USER_REQUEST_ACCESS_TOKEN));
}
export function loginMepUser(opt: LoginOption): IRequestPromise<ClientResponse> {
  const initialRequest = getAccessTokenByEmailPassword(opt);

  return new RequestPromise((resolve, reject) => {
    initialRequest
      .then((response: ClientResponse) => {
        // for SA PA, login success will return user object directly
        let token = response.data;
        if (token) {
          sdkConfig.accessToken = token;
        }

        return verifyToken(token, opt.rememberMe);
      })
      .then(resolve)
      .catch(reject);
  }, initialRequest.requestId);
}

function loginWithAccessToken(accessToken: string): Promise<MxUser> {
  return new Promise((resolve, reject) => {
    verifyToken(accessToken, true)
      .then((response: ClientResponse) => {
        loginPromise.resolve = resolve;
        loginPromise.reject = reject;
        loginPromise.timerId = window.setTimeout(onLoginTimeout, LOGIN_TIMEOUT);

        if (getByPath(response, 'object.user')) {
          cacheMgr.setCurrentVerifyTokenResponseUser(response.object.user);
        }

        // open websocket connection
        Connection.getInstance().open();
      })
      .catch((e) => {
        reject(e);
      });
  });
}

export function logout(logoutAllDevice?: boolean): RequestPromise<void> {
  let reqType = logoutAllDevice
    ? ClientRequestType.USER_REQUEST_LOGOUT_ALL_DEVICES
    : ClientRequestType.USER_REQUEST_LOGOUT;
  return sendBizServerRequest(requestNode(reqType));
}

function registerUser(user: User, token?: string, isPartnerAdmin?: boolean): RequestPromise<User> {
  let params: ClientParam[] = [];
  let type = ClientRequestType.USER_REQUEST_REGISTER_LOCAL_USER;
  let tokenName = ClientRequestParameter.GROUP_REQUEST_INVITE_TOKEN;
  if (isPartnerAdmin) {
    type = ClientRequestType.USER_REQUEST_REGISTER;
    tokenName = ClientRequestParameter.PARTNER_REQUEST_INVITE_TOKEN;
    params.push({
      name: ClientRequestParameter.USER_REQUEST_LOGIN_PARTNER_ADMIN_EXPECTED,
    });
  }

  if (token) {
    params.push({
      name: tokenName,
      string_value: token,
    });
  }
  return sendBizServerRequest(userRequestNode(type, user, params));
}

export function readPasswordRule(): Promise<ClientResponse> {
  return sendBizServerRequest(requestNode(ClientRequestType.USER_REQUEST_READ_PASSWORD_RULE));
}

export function readSsoOptions(): Promise<ClientResponse> {
  return sendBizServerRequest(requestNode(ClientRequestType.USER_REQUEST_READ_SSO_OPTIONS));
}

export function decodeViewToken(token: string, tokenType?: MxTokenType): Promise<ClientResponse> {
  let requestType: ClientRequestType;
  let paramName: ClientRequestParameter;

  if (tokenType === MxTokenType.BOARD_VIEW_TOKEN) {
    requestType = ClientRequestType.BOARD_REQUEST_VIEW_INVITATION;
    paramName = ClientRequestParameter.BOARD_REQUEST_VIEW_TOKEN;
  } else if (tokenType === MxTokenType.GROUP_INVITE_TOKEN) {
    requestType = ClientRequestType.GROUP_REQUEST_VIEW_INVITATION;
    paramName = ClientRequestParameter.GROUP_REQUEST_INVITE_TOKEN;
  } else if (tokenType === MxTokenType.PARTNER_INVITE_TOKEN) {
    requestType = ClientRequestType.PARTNER_REQUEST_VIEW_INVITATION;
    paramName = ClientRequestParameter.PARTNER_REQUEST_INVITE_TOKEN;
  } else if (tokenType === MxTokenType.QR_TOKEN) {
    requestType = ClientRequestType.USER_REQUEST_VIEW_QR_TOKEN;
    paramName = ClientRequestParameter.GROUP_REQUEST_USER_TOKEN;
  } else if (tokenType === MxTokenType.EMAIL_VERIFICATION_TOKEN) {
    requestType = ClientRequestType.USER_REQUEST_PREVIEW_EMAIL_TOKEN;
    return sendBizServerRequest(userRequestNode(requestType, { email_verification_token: token }));
  } else {
    return Promise.reject(MxErr.InvalidParam());
  }

  let params: ClientParam[] = [
    {
      name: paramName,
      string_value: token,
    },
  ];

  return sendBizServerRequest(requestNode(requestType, params));
}

export function verifyEmailToken(token: string): Promise<ClientResponse> {
  return sendBizServerRequest(
    userRequestNode(ClientRequestType.USER_REQUEST_VERIFY_EMAIL_TOKEN, {
      email_verification_token: token,
    }),
  );
}

export function verifyEmailCode(
  email: string,
  code: string,
  userId?: string,
): Promise<ClientResponse> {
  return sendBizServerRequest(
    userRequestNode(ClientRequestType.USER_REQUEST_VERIFY_LOCAL_EMAIL_CODE, {
      email: email,
      email_verification_code: code,
      id: userId,
    }),
  );
}

export function verifyGlobalEmailCode(email: string, code: string): Promise<ClientResponse> {
  return sendBizServerRequest(
    userRequestNode(ClientRequestType.USER_REQUEST_VERIFY_EMAIL_CODE, {
      email: email,
      email_verification_code: code,
    }),
  );
}

export function verifyGlobalSmsCode(phoneNum: string, code: string): Promise<ClientResponse> {
  return sendBizServerRequest(
    userRequestNode(ClientRequestType.USER_REQUEST_VERIFY_EMAIL_CODE, {
      phone_number: phoneNum,
      email_verification_code: code,
    }),
  );
}

export function verifySmsCode(
  phoneNum: string,
  code: string,
  userId?: string,
): Promise<ClientResponse> {
  return sendBizServerRequest(
    userRequestNode(ClientRequestType.USER_REQUEST_VERIFY_LOCAL_SMS_CODE, {
      phone_number: phoneNum,
      email_verification_code: code,
      id: userId,
    }),
  );
}

export function verifyAppleJWT(jwt: string): Promise<ClientResponse> {
  let params: ClientParam[] = [
    {
      name: ClientRequestParameter.USER_REQUEST_APPLE_JWT,
      string_value: jwt,
    },
  ];

  return sendBizServerRequest(
    userRequestNode(ClientRequestType.USER_REQUEST_VERIFY_LOCAL_APPLE_JWT, {}, params),
  );
}

export function verifyGoogleJWT(jwt: string): Promise<ClientResponse> {
  let params: ClientParam[] = [
    {
      name: ClientRequestParameter.USER_REQUEST_GOOGLE_JWT,
      string_value: jwt,
    },
  ];

  return sendBizServerRequest(
    userRequestNode(ClientRequestType.USER_REQUEST_VERIFY_LOCAL_GOOGLE_JWT, {}, params),
  );
}

export function resendVerifyCodeEmail(
  email: string,
  qrToken?: string,
  action?: MxVerifyCodeAction,
  userId?: string,
): Promise<ClientResponse> {
  let params: ClientParam[] = [];
  if (qrToken) {
    params.push({
      name: ClientRequestParameter.GROUP_REQUEST_USER_TOKEN,
      string_value: qrToken,
    });
  }

  if (action === MxVerifyCodeAction.REGISTER) {
    params.push({
      name: ClientRequestParameter.USER_REQUEST_CODE_TO_REGISTER,
    });
  } else if (action === MxVerifyCodeAction.RESET_PASSWORD) {
    params.push({
      name: ClientRequestParameter.USER_REQUEST_CODE_TO_RESET_PASSWORD,
    });
  }

  return sendBizServerRequest(
    userRequestNode(
      ClientRequestType.USER_REQUEST_RESEND_LOCAL_VERIFICATION_CODE_EMAIL,
      { email: email, id: userId },
      params,
    ),
  );
}

export function resendGlobalVerifyCodeEmail(email: string): Promise<ClientResponse> {
  return sendBizServerRequest(
    userRequestNode(ClientRequestType.USER_REQUEST_RESEND_VERIFICATION_CODE_EMAIL, {
      email: email,
    }),
  );
}

export function resendGlobalVerifyCodeSms(phoneNum: string): Promise<ClientResponse> {
  return sendBizServerRequest(
    userRequestNode(ClientRequestType.USER_REQUEST_RESEND_VERIFICATION_CODE_EMAIL, {
      phone_number: phoneNum,
    }),
  );
}

export function resendVerifyCodeSms(
  phoneNum: string,
  qrToken?: string,
  action?: MxVerifyCodeAction,
  userId?: string,
): Promise<ClientResponse> {
  let params: ClientParam[] = [];
  if (qrToken) {
    params.push({
      name: ClientRequestParameter.GROUP_REQUEST_USER_TOKEN,
      string_value: qrToken,
    });
  }

  if (action === MxVerifyCodeAction.REGISTER) {
    params.push({
      name: ClientRequestParameter.USER_REQUEST_CODE_TO_REGISTER,
    });
  } else if (action === MxVerifyCodeAction.RESET_PASSWORD) {
    params.push({
      name: ClientRequestParameter.USER_REQUEST_CODE_TO_RESET_PASSWORD,
    });
  }

  return sendBizServerRequest(
    userRequestNode(
      ClientRequestType.USER_REQUEST_RESEND_LOCAL_VERIFICATION_CODE_SMS,
      { phone_number: phoneNum, id: userId },
      params,
    ),
  );
}

export function resendViewToken(oldViewToken: string): Promise<ClientResponse> {
  let params: ClientParam[] = [
    {
      name: ClientRequestParameter.BOARD_REQUEST_VIEW_TOKEN,
      string_value: oldViewToken,
    },
  ];

  return sendBizServerRequest(
    requestNode(ClientRequestType.BOARD_REQUEST_RESEND_VIEW_TOKEN, params),
  );
}

export function registerWithQRToken(
  user: User,
  qrToken: string,
  noRelationBoard: boolean,
  isInternalUser: boolean,
): Promise<User> {
  let params: ClientParam[] = [];
  if (noRelationBoard) {
    params.push({
      name: ClientRequestParameter.GROUP_REQUEST_NO_RELATION_BOARD,
    });
  }

  if (isInternalUser) {
    params.push({
      name: ClientRequestParameter.GROUP_REQUEST_INVITE_TOKEN,
      string_value: qrToken,
    });
  } else {
    user.type = UserType.USER_TYPE_LOCAL;
    user.relations = [
      {
        user: {
          qr_tokens: [
            {
              token: qrToken,
            },
          ],
        },
      },
    ];
  }

  return sendBizServerRequest(
    userRequestNode(ClientRequestType.USER_REQUEST_REGISTER_LOCAL_USER_WITH_QR_TOKEN, user, params),
  )
    .then((response: ClientResponse) => {
      return loginWithAccessToken(response.data);
    })
    .catch((e) => {
      return Promise.reject(e);
    });
}

export function registerFreemiumUser(
  user: User,
  option: MxRegisterUserOption,
): RequestPromise<User> {
  let params: ClientParam[] = [];

  const { method, authorization, qrToken, inviteToken, isInternalUser, noRelationBoard } = option;

  if (noRelationBoard) {
    params.push({
      name: ClientRequestParameter.GROUP_REQUEST_NO_RELATION_BOARD,
    });
  }

  if (!isInternalUser) user.type = UserType.USER_TYPE_LOCAL;

  if (inviteToken) {
    params.push({
      name: ClientRequestParameter.GROUP_REQUEST_INVITE_TOKEN,
      string_value: inviteToken,
    });
  }

  if (qrToken) {
    user.relations = [
      {
        user: {
          qr_tokens: [
            {
              token: qrToken,
            },
          ],
        },
      },
    ];
  }

  let reqType = ClientRequestType.USER_REQUEST_REGISTER_LOCAL_USER_WITH_QR_TOKEN;
  if (method === MxRegisterUserMethod.INVITE_TOKEN || method === MxRegisterUserMethod.QR_TOKEN) {
    reqType = ClientRequestType.USER_REQUEST_REGISTER_LOCAL_USER_WITH_QR_TOKEN;
  } else if (method === MxRegisterUserMethod.APPLE_JWT) {
    reqType = ClientRequestType.USER_REQUEST_REGISTER_LOCAL_USER_WITH_QR_TOKEN_BY_APPLE_ID;
    if (inviteToken) {
      reqType = ClientRequestType.USER_REQUEST_REGISTER_LOCAL_USER_BY_APPLE_ID;
    }
    params.push({
      name: ClientRequestParameter.USER_REQUEST_APPLE_JWT,
      string_value: authorization,
    });
  } else if (method === MxRegisterUserMethod.GOOGLE_JWT) {
    reqType = ClientRequestType.USER_REQUEST_REGISTER_LOCAL_USER_WITH_QR_TOKEN_BY_GOOGLE_ID;
    if (inviteToken) {
      reqType = ClientRequestType.USER_REQUEST_REGISTER_LOCAL_USER_BY_GOOGLE_ID;
    }
    params.push({
      name: ClientRequestParameter.USER_REQUEST_GOOGLE_JWT,
      string_value: authorization,
    });
  } else if (method === MxRegisterUserMethod.EMAIL_CODE) {
    reqType = ClientRequestType.USER_REQUEST_REGISTER_LOCAL_USER_WITH_QR_TOKEN_BY_VERIFICATION_CODE;
    params.push({
      name: ClientRequestParameter.USER_REQUEST_EMAIL_CODE,
      string_value: authorization,
    });
  } else if (method === MxRegisterUserMethod.SMS_CODE) {
    reqType = ClientRequestType.USER_REQUEST_REGISTER_LOCAL_USER_WITH_QR_TOKEN_BY_VERIFICATION_CODE;
    params.push({
      name: ClientRequestParameter.USER_REQUEST_SMS_CODE,
      string_value: authorization,
    });
  }

  return sendBizServerRequest(userRequestNode(reqType, user, params));
}

export function checkUserPermission(user: User, loginOpts: LoginOption) {
  if (!user) {
    throw new BizServerError(ClientResponseCode.RESPONSE_ERROR_PERMISSION, '', 'user expected');
  }

  let userGroups = (user.groups || []).filter((group) => !group.is_deleted);
  let userPartners = (user.partners || []).filter((partner) => !partner.is_deleted);
  let userRole: UserRole = user.role || UserRole.USER_ROLE_NORMAL;
  let userGroupRole: GroupAccessType =
    userGroups.length > 0 ? userGroups[0].type : GroupAccessType.GROUP_NO_ACCESS;

  const ORG_ADMIN_ROLES: GroupAccessType[] = [
    GroupAccessType.GROUP_OWNER_ACCESS,
    GroupAccessType.GROUP_ADMIN_ACCESS,
  ];

  const SUPER_ADMIN_ROLES: UserRole[] = [
    UserRole.USER_ROLE_SUPERADMIN,
    UserRole.USER_ROLE_OBJECT_READ,
    UserRole.USER_ROLE_OBJECT_WRITE,
    UserRole.USER_ROLE_SUPERADMIN_READONLY,
  ];

  let isOrgAdmin: boolean = ORG_ADMIN_ROLES.indexOf(userGroupRole) > -1 ? true : false;
  let isSuperAdmin: boolean = SUPER_ADMIN_ROLES.indexOf(userRole) > -1 ? true : false;
  let isPartnerAdmin: boolean = userPartners.length > 0 ? true : false;
  let userOrgId = userGroups.length > 0 ? userGroups[0].group.id : '';

  let expectedRoles: LoginOption = loginOpts || {
    isOrgAdmin: false,
    isSuperAdmin: false,
    isPartnerAdmin: false,
  };
  let expectIsOrgAdmin: boolean = expectedRoles.isOrgAdmin || false;
  let expectIsSuperAdmin: boolean = expectedRoles.isSuperAdmin || false;
  let expectIsPartnerAdmin: boolean = expectedRoles.isPartnerAdmin || false;
  let expectNormalUser: boolean = !(expectIsOrgAdmin || expectIsSuperAdmin || expectIsPartnerAdmin);
  let expectUserOrgId = ''; //cacheMgr.anonymousOrg ? cacheMgr.anonymousOrg.id : '';

  if (expectNormalUser) {
    // do not allow SA PA to access web portal
    if (userOrgId && expectUserOrgId === userOrgId) {
      // for dirty data: some super admin account has user group, allow it to access web portal
    } else if (isSuperAdmin || isPartnerAdmin) {
      throw new BizServerError(
        'normal user expected',
        ClientResponseCode.RESPONSE_ERROR_PERMISSION,
      );
    }
  }

  if (expectIsOrgAdmin) {
    // allow OA SA PA to access admin portal
    if (!isOrgAdmin && !isSuperAdmin && !isPartnerAdmin) {
      throw new BizServerError('org admin expected', ClientResponseCode.RESPONSE_ERROR_PERMISSION);
    }
  }

  if (expectIsPartnerAdmin) {
    // allow SA PA to access partner admin portal
    if (!isSuperAdmin && !isPartnerAdmin) {
      throw new BizServerError(
        'partner admin expected',
        ClientResponseCode.RESPONSE_ERROR_PERMISSION,
      );
    }
  }

  if (expectIsSuperAdmin) {
    // allow SA to access super admin portal
    if (!isSuperAdmin) {
      throw new BizServerError(
        'super admin expected',
        ClientResponseCode.RESPONSE_ERROR_PERMISSION,
      );
    }
  }

  return {
    isOrgAdmin: expectIsOrgAdmin,
    isSuperAdmin: expectIsSuperAdmin,
    isPartnerAdmin: expectIsPartnerAdmin,
  };
}

export function sendUserKeepAliveRequest() {
  return sendBizServerRequest(requestNode(ClientRequestType.USER_REQUEST_KEEP_ALIVE));
}
