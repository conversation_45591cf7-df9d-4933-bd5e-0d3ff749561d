import {
  ClientRequest,
  ClientRequestType,
  ClientRequestParameter,
  ClientParam,
  User,
  Board,
  Group,
  Contacts,
  ActionObject,
  Partner,
  WebApp,
  TelephonyDomain,
} from '@moxo/proto';

export function requestNode(
  type: ClientRequestType,
  params?: ClientParam[],
  user?: User,
  board?: Board,
  group?: Group,
  contacts?: Contacts,
): ClientRequest {
  let req: ClientRequest = { type: type, object: {} };
  if (user) {
    req.object!.user = user;
  }
  if (board) {
    req.object!.board = board;
  }
  if (group) {
    req.object!.group = group;
  }
  if (contacts) {
    req.object!.contacts = contacts;
  }
  if (params) {
    req.params = params;
  }

  return req;
}

export function userRequestNode(
  type: ClientRequestType,
  user: User,
  params?: ClientParam[],
): ClientRequest {
  return requestNode(type, params, user, null, null);
}

export function boardRequestNode(
  type: ClientRequestType,
  board: Board,
  params?: ClientParam[],
): ClientRequest {
  // if (board && board.id) {
  //   let mxBoard: MxBoard = getBoardById(board.id);
  //   if (!mxBoard) {
  //     // try to find instant board
  //     mxBoard = getInstantBoardById(board.id);
  //   }
  //
  //   if (mxBoard && mxBoard.option) {
  //     return boardRequestNodeWithOption(type, board, params, mxBoard.option);
  //   }
  // }

  return requestNode(type, params, null, board, null);
}

// export function boardRequestNodeWithOption(
//   type: ClientRequestType,
//   board: Board,
//   params?: ClientParam[],
//   option?: MxBoardOption,
// ): ClientRequest {
//   let viewAsGroup: Group = null;
//   if (option) {
//     if (option.userId) {
//       viewAsGroup = {
//         members: [
//           {
//             user: {
//               id: option.userId,
//             },
//           },
//         ],
//       };
//     }
//
//     if (option.viewToken) {
//       if (!params) {
//         params = [];
//       }
//
//       params.push({
//         name: ClientRequestParameter.BOARD_REQUEST_VIEW_TOKEN,
//         string_value: option.viewToken,
//       });
//     }
//
//     if (option.suppressFeed) {
//       if (!params) {
//         params = [];
//       }
//
//       let hasSuppressFeedParam: boolean =
//         params.filter((p) => p.name === ClientRequestParameter.BOARD_REQUEST_SUPPRESS_FEED).length >
//         0
//           ? true
//           : false;
//       if (!hasSuppressFeedParam) {
//         params.push({
//           name: ClientRequestParameter.BOARD_REQUEST_SUPPRESS_FEED,
//         });
//       }
//     }
//   }
//   return requestNode(type, params, null, board, viewAsGroup);
// }

export function groupRequestNode(
  type: ClientRequestType,
  group: Group,
  params?: ClientParam[],
): ClientRequest {
  return requestNode(type, params, null, null, group);
}

export function contactRequestNode(
  type: ClientRequestType,
  contacts: Contacts,
  params?: ClientParam[],
): ClientRequest {
  return requestNode(type, params, null, null, null, contacts);
}

export function sessionRequestNode(
  type: ClientRequestType,
  session: ActionObject,
  params?: ClientParam[],
): ClientRequest {
  let req: ClientRequest = { type: type, object: { session: session } };
  if (params) {
    req.params = params;
  }
  return req;
}

export function boardAndSessionRequestNode(
  type: ClientRequestType,
  board: Board,
  session: ActionObject,
  params?: ClientParam[],
): ClientRequest {
  let req: ClientRequest = { type: type, object: {} };
  if (board) {
    req.object!.board = board;
  }
  if (session) {
    req.object!.session = session;
  }
  if (params) {
    req.params = params;
  }

  return req;
}

export function partnerRequestNode(
  type: ClientRequestType,
  partner: Partner,
  params?: ClientParam[],
): ClientRequest {
  let req: ClientRequest = { type: type, object: {} };
  if (partner) {
    req.object!.partner = partner;
  }
  if (params) {
    req.params = params;
  }

  return req;
}

export function webappRequestNode(
  type: ClientRequestType,
  webapp: WebApp,
  params?: ClientParam[],
): ClientRequest {
  let req: ClientRequest = { type: type, object: {} };
  if (webapp) {
    req.object!.webapp = webapp;
  }
  if (params) {
    req.params = params;
  }

  return req;
}

export function telephonyDomainRequestNode(
  type: ClientRequestType,
  domain: TelephonyDomain,
  params?: ClientParam[],
): ClientRequest {
  let req: ClientRequest = { type: type, object: {} };
  if (domain) {
    req.object!.telephony_domain = domain;
  }
  if (params) {
    req.params = params;
  }

  return req;
}
