import utils, { cleanDir } from './utils.mjs';
import ora from 'ora';
import ProtoBuf from 'protobufjs';
import array from 'lodash/array.js';
import protobuf from 'protobufjs';
import path from 'path';
import fs from 'fs';

const newLine = '\r\n';
const indent = '  ';

function buildEnumContent(message) {
  const out = [];
  const val = [];
  out.push('export enum ' + message.name + ' {' + newLine);
  Object.keys(message.values).forEach(function (key) {
    val.push(`  ${key} = '${key}'`);
  });
  out.push(val.join(',' + newLine) + newLine);
  out.push('}' + newLine);
  return out.join('');
}
const requiredFields = ['id', 'object'];
function buildMessageContent(message) {
  const result = [];
  let depends = [];
  result.push('export interface ' + message.name + ' {' + newLine);
  Object.keys(message.fields).forEach(function (key) {
    const field = message.fields[key];
    if (field instanceof ProtoBuf.Field) {
      let type = field.type;
      if (type === 'uint64' || type === 'int64' || type === 'uint32' || type == 'int32') {
        type = 'number';
      } else if (type === 'bool') {
        type = 'boolean';
      } else if (type === 'bytes') {
        type = 'string';
      } else if (type == 'JobStatistics' || type == 'Job') {
        type = 'any';
      }
      const optionSymbol = requiredFields.includes(field.name) ? '' : '?';
      if (field.repeated) {
        result.push(indent + field.name + optionSymbol + ': ' + type + '[]' + newLine);
      } else {
        result.push(indent + field.name + optionSymbol + ': ' + type + '' + newLine);
      }
      if (!/number|string|boolean|any/.test(type)) {
        depends.push(type);
      }
    }
  });
  result.push('}' + newLine);

  const header = [];
  depends = array.union(depends);
  depends.forEach(function (name) {
    if (name !== message.name) {
      header.push('import {' + name + "} from './" + name + "'" + newLine);
    }
  });
  return header.concat(result).join('');
}

export function protoBuf2TS(sourceFile, outputPath) {
  const root = new ProtoBuf.Root();
  const fileList = [];

  function pushToIndex(name, isEnum) {
    fileList.push({
      text: `export * from './${name}'` + newLine,
      isEnum: isEnum,
    });
  }

  utils.cleanDir(utils.resolve(outputPath, './src'));
  utils.cleanDir(utils.resolve(outputPath, './types'));
  return new Promise((resolve, reject) => {
    root.load(utils.resolve(sourceFile), { keepCase: true }, function (error, root) {
      if (error) {
        console.error(error);
        reject();
      }
      Object.keys(root).forEach(function (key) {
        const Message = root.lookup(key);
        if (Message) {
          if (Message instanceof ProtoBuf.Enum) {
            const content = buildEnumContent(Message);
            const toFile = utils.resolve(outputPath, 'src', `${Message.name}.ts`);
            utils.writeFile(toFile, content);
            pushToIndex(Message.name, true);
          } else if (Message instanceof ProtoBuf.Type) {
            const content = buildMessageContent(Message);
            const toFile = utils.resolve(outputPath, 'types', `${Message.name}.d.ts`);
            utils.writeFile(toFile, content);
            pushToIndex(Message.name, false);
          } else {
            // console.log(key);
          }
        }
      });
      utils.writeFile(
        utils.resolve(outputPath, 'src', 'index.ts'),
        fileList
          .filter((item) => item.isEnum)
          .map((item) => item.text)
          .join(''),
      );
      utils.writeFile(
        utils.resolve(outputPath, 'types', 'index.d.ts'),
        fileList.map((item) => item.text).join(''),
      );
      resolve();
    });
  });
}

export function protoBuf2TSJSON(sourceFile, outputPath, varName) {
  const root = new protobuf.Root();
  return new Promise((resolve, reject) => {
    root.load(utils.resolve(sourceFile), { keepCase: true }, function (error, builder) {
      if (error) {
        return reject(error);
      }
      const content = 'export const ' + varName + ' = ' + JSON.stringify(builder.toJSON()) + '\r\n';
      const toFile = utils.resolve(outputPath, `${varName}.ts`);
      utils.writeFile(toFile, content);
      resolve();
    });
  });
}
